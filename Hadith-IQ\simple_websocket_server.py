#!/usr/bin/env python3
"""
Simple WebSocket server to resolve the Flutter app connection issue.
This provides the basic WebSocket endpoint that the Flutter app expects.
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
import asyncio
import json
import uvicorn
import sys

app = FastAPI(
    title="Simple WebSocket Server",
    description="Basic server to handle Flutter WebSocket connections",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Store connected clients
connected_clients = []

@app.get("/")
async def root():
    return {"message": "Simple WebSocket Server is running!", "status": "online"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "message": "Server is running properly"}

@app.websocket("/ws/status")
async def websocket_status(websocket: WebSocket):
    """WebSocket endpoint for server status updates"""
    print("New WebSocket connection attempt")
    await websocket.accept()
    connected_clients.append(websocket)
    print(f"Client connected. Total clients: {len(connected_clients)}")

    try:
        # Send initial status
        status_message = {"status": "online"}
        await websocket.send_text(json.dumps(status_message))
        print("Sent initial status message")

        # Keep connection alive and send periodic status updates
        while True:
            await asyncio.sleep(5)  # Send status every 5 seconds
            status_message = {"status": "online"}
            await websocket.send_text(json.dumps(status_message))
            print("Sent periodic status update")

    except WebSocketDisconnect:
        print("Client disconnected")
        if websocket in connected_clients:
            connected_clients.remove(websocket)
    except Exception as e:
        print(f"WebSocket error: {e}")
        if websocket in connected_clients:
            connected_clients.remove(websocket)

@app.get("/api/test")
async def test_endpoint():
    return {
        "message": "Test endpoint working",
        "backend": "FastAPI",
        "status": "operational",
        "connected_clients": len(connected_clients)
    }

if __name__ == "__main__":
    print("Starting Simple WebSocket Server...")
    print("WebSocket endpoint: ws://localhost:8000/ws/status")
    print("Health check: http://localhost:8000/health")
    print("Press Ctrl+C to stop the server")
    sys.stdout.flush()

    try:
        uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Server error: {e}")
        sys.exit(1)
